import 'dart:async';

import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/reflection_request.dart';
import 'package:mixpanel_flutter/mixpanel_flutter.dart';

import '../features/home/<USER>/models/check_in_request.dart';
// Import material.dart

class MixpanelService {
  late Mixpanel _mixpanel;
  String?
      _currentUserId; // Track current user ID to prevent duplicate identify calls

  Future<void> init(String token) async {
    _mixpanel = await Mixpanel.init(token, trackAutomaticEvents: true);
    // Opt-out of tracking for testing/debugging
    // _mixpanel.setOptOutTracking(true);
  }

  void identify(String userId) {
    // Check if we're already identified with this user ID
    if (_currentUserId == userId) {
      info('User already identified with ID: $userId');
      return;
    }

    try {
      _mixpanel.reset();
      _mixpanel.identify(userId);
      _mixpanel.getPeople().set("User ID", userId);
      _currentUserId = userId; // Store the current user ID
      info('Successfully identified user: $userId');
    } catch (e) {
      info('Error identifying user $userId: $e');
      // If there's an error, try to reset first and then identify
      if (e.toString().contains('erranondistinctidassignedalready')) {
        info('Attempting to reset and re-identify...');
        reset();
        try {
          _mixpanel.identify(userId);
          _mixpanel.getPeople().set("User ID", userId);
          _currentUserId = userId;
          info('Successfully re-identified user after reset: $userId');
        } catch (retryError) {
          info('Failed to re-identify user after reset: $retryError');
        }
      }
    }
  }

  void reset() {
    _mixpanel.reset();
    _currentUserId = null; // Clear the tracked user ID
    info('Mixpanel session reset');
  }

  // Alternative method for switching users safely
  void switchUser(String newUserId) {
    if (_currentUserId != null && _currentUserId != newUserId) {
      // Reset the session before identifying a new user
      reset();
    }
    identify(newUserId);
  }

  void setUserProperties(Map<String, dynamic> properties) {
    try {
      final people = _mixpanel.getPeople();
      properties.forEach((key, value) {
        people.set(key, value);
      });
    } catch (e) {
      info('Error setting user properties: $e');
    }
  }

  void trackScreenView(String screenName, {Map<String, dynamic>? properties}) {
    try {
      final allProperties = {
        'Screen Name': screenName,
        if (properties != null) ...properties,
      };

      _mixpanel.track('Screen View', properties: allProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking screen view: $e');
    }
  }

  void trackSearchQuery(String pageName, String queryName) {
    try {
      final searchProperties = {
        'Search Query': queryName,
        'Screen Name': pageName,
      };

      _mixpanel.track('Search Performed', properties: searchProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking search query: $e');
    }
  }

  void trackSubmissionWithOptions(
    String pageName, {
    Map<String, dynamic>? properties,
    List<Question>? questions,
    List<Answer>? answers,
    Map<String, String>? feedbackNames,
  }) {
    try {
      final allProperties = {
        'Screen Name': pageName,
        if (properties != null) ...properties,
      };

      // Map question ID and answer ID to properties
      if (questions != null && questions.isNotEmpty) {
        for (int i = 0; i < questions.length; i++) {
          final question = questions[i];

          if (question.questionId != null) {
            allProperties['Question ID ${i + 1}'] = question.questionId;
          }

          if (question.answer != null) {
            allProperties['Answer Text ${i + 1}'] = question.answer;
          }

          if (question.answerId != null) {
            allProperties['Answer ID ${i + 1}'] = question.answerId;
          }
        }
      }

      // Map Answer data to properties
      if (answers != null && answers.isNotEmpty) {
        for (int i = 0; i < answers.length; i++) {
          final answer = answers[i];

          if (answer.questionId != null) {
            allProperties["Question ID ${i + 1}"] = answer.questionId;
          }

          if (answer.selectedChoiceId != null) {
            allProperties['Selected Choice ID ${i + 1}'] =
                answer.selectedChoiceId;
          }

          if (answer.answerText != null) {
            allProperties['Answer Text ${i + 1}'] = answer.answerText;
          }
        }
      }

      // Add feedback names and values
      if (feedbackNames != null && feedbackNames.isNotEmpty) {
        feedbackNames.forEach((name, value) {
          allProperties[name] = value;
        });
      }

      _mixpanel.track('Options Submitted', properties: allProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking submission: $e');
    }
  }

  void trackCheckInWithOptions(
    String pageName, {
    Map<String, dynamic>? properties,
    List<Question>? questions,
  }) {
    try {
      final allProperties = {
        'Screen Name': pageName,
        if (properties != null) ...properties,
      };

      // Map question ID and answer ID to properties
      if (questions != null && questions.isNotEmpty) {
        for (int i = 0; i < questions.length; i++) {
          final question = questions[i];
          allProperties['Check In Option ${i + 1}'] = question.toJson();
        }
      }

      _mixpanel.track('Check In Options Selected', properties: allProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking check-in: $e');
    }
  }

  void trackTextAnimation(String exerciseId, String pageName,
      {Map<String, dynamic>? additionalProperties}) {
    try {
      // Create properties map with required fields
      final Map<String, dynamic> properties = {
        'Exercise ID': exerciseId,
        'Page Name': pageName,
        'Timestamp': DateTime.now().toIso8601String(),
      };

      // Add any additional properties if provided
      if (additionalProperties != null) {
        properties.addAll(additionalProperties);
      }

      // Track the event using the existing trackScreenView method
      trackScreenView('Text Animation', properties: properties);
    } catch (e) {
      info('Error tracking text animation: $e');
    }
  }

  void trackButtonClick(String buttonName, {Map<String, dynamic>? properties}) {
    try {
      final allProperties = {
        'Button Name': buttonName,
        if (properties != null) ...properties,
      };

      _mixpanel.track('Button Click', properties: allProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking button click: $e');
    }
  }

  void trackEvent(String eventName, {Map<String, dynamic>? properties}) {
    try {
      _mixpanel.track(eventName, properties: properties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking event: $e');
    }
  }

  void trackNotificationPermission(bool isGranted, String platform,
      {Map<String, dynamic>? properties}) {
    try {
      final allProperties = {
        'Platform': platform,
        'Status': isGranted ? 'Granted' : 'Denied',
        'Timestamp': DateTime.now().toIso8601String(),
        if (properties != null) ...properties,
      };

      _mixpanel.track('Notification Permission Status',
          properties: allProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking notification permission: $e');
    }
  }

  void trackPushNotification(String notificationType,
      {Map<String, dynamic>? properties}) {
    try {
      final allProperties = {
        'Notification Type': notificationType,
        'Timestamp': DateTime.now().toIso8601String(),
        if (properties != null) ...properties,
      };

      _mixpanel.track('Push Notification Received', properties: allProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking push notification: $e');
    }
  }

  // Utility method to get current user ID
  String? getCurrentUserId() {
    return _currentUserId;
  }

  // Method to check if user is already identified
  bool isUserIdentified(String userId) {
    return _currentUserId == userId;
  }
}
