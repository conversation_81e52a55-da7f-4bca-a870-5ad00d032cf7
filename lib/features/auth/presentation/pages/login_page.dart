import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/firebase_push.dart';
import 'package:gotcha_mfg_app/core/mixpanel_service.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/core/utils/device_info.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/login_request_model.dart';
import 'package:gotcha_mfg_app/features/auth/presentation/blocs/login/login_cubit.dart';
import 'package:gotcha_mfg_app/features/auth/presentation/blocs/login/login_state.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_safe_area.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';

import '../../../../core/storage/token_manager.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../../notification/presentation/blocs/notification/notification_cubit.dart';
import '../../../profile/presentation/cubits/profile_links/profile_links_cubit.dart';
import '../../data/models/fcm_req_model.dart';
import '../blocs/fcm_update/fcm_update_cubit.dart';
import '../widgets/auth_text_field.dart';

@RoutePage()
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  TextEditingController username = TextEditingController();
  TextEditingController password = TextEditingController();
  String? emailError;
  String? passwordError;
  bool hasEnteredEmail = false;
  bool hasEnteredPassword = false;
  bool _isButtonEnabled = false;

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Login Page',
      properties: {'Code': 'screen_view.login_page'},
    );
  }

  @override
  void dispose() {
    username.dispose();
    password.dispose();
    super.dispose();
  }

  bool isValidEmail(String email) {
    return RegExp(
            r"^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$")
        .hasMatch(email);
  }

  void validateEmail() {
    if (!hasEnteredEmail) return;

    setState(() {
      if (username.text.isEmpty) {
        emailError = 'Email is required';
      } else if (!isValidEmail(username.text)) {
        emailError = 'Please enter a valid email address';
      } else {
        emailError = null;
      }
    });
  }

  void validatePassword() {
    if (!hasEnteredPassword) return;

    setState(() {
      if (password.text.isEmpty) {
        passwordError = 'Password is required';
      } else if (password.text.length < 6) {
        passwordError = 'Password must be at least 6 characters';
      } else {
        passwordError = null;
      }
    });
  }

  bool validateFields() {
    setState(() {
      hasEnteredEmail = true;
      hasEnteredPassword = true;
    });

    validateEmail();
    validatePassword();

    return emailError == null && passwordError == null;
  }

  void _setButtonState() {
    setState(() {
      _isButtonEnabled = username.text.isNotEmpty &&
          password.text.isNotEmpty &&
          emailError == null &&
          passwordError == null;
    });
  }

  Future<void> validateAndLogin() async {
    if (validateFields()) {
      final String currentTimeZone = await FlutterTimezone.getLocalTimezone();
      context.read<AuthCubit>().login(
            LoginRequest(
              username: username.text,
              password: password.text,
              timezone: currentTimeZone,
            ),
          );
    }
  }

  saveToken(String access, String refresh) async {
    await sl<TokenManager>().saveTokens(
      accessToken: access,
      refreshToken: refresh,
    );
  }

  Future<String?> deviceId() async {
    // Made return type nullable for safety
    // Assuming getDeviceId() returns a String or null.
    return await getDeviceId();
  }

  Future<String?> fcmToken() async {
    try {
      // Clear the current token first
      await MFGPushNotification.messaging.deleteToken();

      // Get new token
      var token = await MFGPushNotification.messaging.getToken();
      return token;
    } catch (e) {
      // Log the error and return null
      info("Error managing FCM token: $e");
      return null;
    }
  }

  Future<void> updateToken(BuildContext context) async {
    try {
      final token = await fcmToken();
      final device = await deviceId();

      // Ensure we have a context after the async operations complete
      if (context.mounted) {
        // Check context is valid before using it
        context
            .read<FcmUpdateCubit>() // Make sure data types match your model
            .updateFcmToken(
              FcmRequestModel(
                // Make sure data types match your model
                token: token ?? '', // Provide a default if null
                fcmDeviceId: device ?? '', // Provide a default if null
              ),
            );
      } else {
        info("Context is no longer valid, cannot update token.");
      }
    } catch (e) {
      info("Error updating token: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.of(context).size;
    return BlocConsumer<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state is AuthLoginError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
        if (state is AuthLoginLoaded) {
          updateToken(context);
          sl<MixpanelService>().reset();
          sl<MixpanelService>()
              .identify(state.loginResponse.data?.deviceId ?? '');
          sl<TokenManager>()
              .saveUniqueId(state.loginResponse.data?.deviceId ?? '');
          saveToken(
            state.loginResponse.data?.accessToken ?? '',
            state.loginResponse.data?.refreshToken ?? '',
          );
          // context.read<SplashCubit>().silentInfo();
          context.read<ProfileLinksCubit>().getProfileLinks();
          context.replaceRoute(
            PersonaliseAccountRoute(
              isPersonalise: true,
              isInfo: false,
            ),
          );
          context.read<NotificationCubit>().getNotificationCount();
        }
      },
      builder: (context, state) {
        if (state is AuthLoading) {
          return SizedBox(
              height: size.height,
              child: const LoadingWidget(color: Colors.white));
        }

        return Scaffold(
            resizeToAvoidBottomInset: false,
            appBar: AppBar(
              toolbarHeight: 0,
              elevation: 0,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.white,
                systemNavigationBarIconBrightness: Brightness.dark,
                statusBarBrightness: Brightness.light,
                statusBarIconBrightness: Brightness.dark,
                systemNavigationBarColor: AppColors.grey,
              ),
            ),
            body: AppSafeArea(
              child: GestureDetector(
                onTap: () {
                  FocusScope.of(context).unfocus();
                },
                behavior: HitTestBehavior.translucent,
                child: Padding(
                  padding: EdgeInsets.only(
                    top: isIos ? 4 : 8,
                    right: 8,
                    left: 8,
                  ),
                  child: Column(
                    children: [
                      AppHeader(
                        title: 'Login',
                        onBackTap: () {
                          Navigator.pop(context);
                        },
                      ),
                      Expanded(
                        child: Container(
                          color: AppColors.navy,
                          padding: const EdgeInsets.only(),
                          child: Container(
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(30),
                                topRight: Radius.circular(30),
                              ),
                              color: AppColors.grey,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(24),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  AuthTextField(
                                    controller: username,
                                    keyboardType: TextInputType.emailAddress,
                                    text: 'Email',
                                    hinttext: 'Enter your email address',
                                    errorText: emailError,
                                    onChanged: (value) {
                                      setState(() {
                                        hasEnteredEmail = true;
                                      });
                                      validateEmail();
                                      _setButtonState();
                                    },
                                  ),
                                  const Gap(16),
                                  AuthTextField(
                                    controller: password,
                                    isPassword: true,
                                    text: 'Password',
                                    hinttext: 'Enter your password',
                                    errorText: passwordError,
                                    onChanged: (value) {
                                      setState(() {
                                        hasEnteredPassword = true;
                                      });
                                      validatePassword();
                                      _setButtonState();
                                    },
                                  ),
                                  const Gap(16),
                                  TextButton(
                                    onPressed: () {
                                      context.pushRoute(
                                          const ForgotPasswordRoute());
                                      sl<MixpanelService>().trackButtonClick(
                                          'Forgot Password',
                                          properties: {
                                            'Page': 'Login Page',
                                            'Code':
                                                'click.login_page.forgot_password'
                                          });
                                    },
                                    child: Text('Forgot password',
                                        style: textTheme.linkText
                                            .copyWith(color: AppColors.coral)),
                                  ),
                                  const Spacer(),
                                  SizedBox(
                                      width: size.width,
                                      child: PrimaryButton(
                                          text: 'Enter',
                                          isEnabled: _isButtonEnabled,
                                          onPressed: () {
                                            if (_isButtonEnabled) {
                                              validateAndLogin();
                                              sl<MixpanelService>()
                                                  .trackButtonClick('Enter',
                                                      properties: {
                                                    'Page': 'Login Page',
                                                    'Code':
                                                        'click.login_page.enter'
                                                  });
                                            } else {
                                              return;
                                            }
                                          })),
                                  const Gap(24),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        'Don\'t have an account?',
                                        style: textTheme.ralewayMedium.copyWith(
                                          color: AppColors.navy,
                                          fontSize: 13,
                                        ),
                                      ),
                                      const Gap(4),
                                      GestureDetector(
                                        onTap: () {
                                          context.replaceRoute(
                                              const SignUpRoute());
                                          sl<MixpanelService>()
                                              .trackButtonClick('Sign Up',
                                                  properties: {
                                                'Page': 'Login Page',
                                                'Code':
                                                    'click.login_page.sign_up'
                                              });
                                        },
                                        child: Text(
                                          'Sign up ',
                                          style: textTheme.ralewayBold.copyWith(
                                            decorationColor: AppColors.coral,
                                            color: AppColors.coral,
                                            fontSize: 13,
                                          ),
                                        ),
                                      ),
                                      Text(
                                        'for free',
                                        style: textTheme.ralewayMedium.copyWith(
                                          color: AppColors.navy,
                                          fontSize: 13,
                                        ),
                                      ),
                                    ],
                                  ),
                                  isIos ? const Gap(20) : const Gap(12),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ));
      },
    );
  }
}
