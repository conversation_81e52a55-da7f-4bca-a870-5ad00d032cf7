import 'package:bloc/bloc.dart';
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';

import '../../../data/models/login_request_model.dart';
import '../../../domain/usecases/delete_account_usecase.dart';
import '../../../domain/usecases/login_usecase.dart';
import 'login_state.dart';

class AuthCubit extends Cubit<AuthState> {
  /// Constructor
  AuthCubit(this._loginUseCase, this._deleteAccountUseCase)
      : super(AuthInitial());

  final LoginUseCase _loginUseCase;
  final DeleteUsecase _deleteAccountUseCase;

  /// Login
  Future<void> login(LoginRequest request) async {
    emit(AuthLoading());
    final result = await _loginUseCase.call(request);
    if (result.isSuccess) {
      emit(AuthLoginLoaded(result.data!));
    } else {
      emit(AuthLoginError(result.error!));
    }
  }

  Future<void> deleteAccount() async {
    emit(AuthDeleteAccountLoading());
    final result = await _deleteAccountUseCase.call(NoParams());
    if (result.isSuccess) {
      emit(AuthDeleteAccountSuccess(result.data!));
    } else {
      emit(AuthDeleteAccountError(result.error!));
    }
  }
}
