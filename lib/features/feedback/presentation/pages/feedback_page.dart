import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/features/feedback/data/models/add_feedback.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';

import '../../../../config/router/app_router.gr.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/snackbar_service.dart';
import '../../../../locator.dart';
import '../../../exercise/presentation/widgets/reflection_choice.dart';
import '../../../exercise/presentation/widgets/reflection_text_field.dart';

@RoutePage()
class FeedBackPage extends StatefulWidget {
  const FeedBackPage({super.key});

  @override
  _FeedBackPageState createState() => _FeedBackPageState();
}

class _FeedBackPageState extends State<FeedBackPage> {
  final PageController _pageController = PageController();

  int pageVal = 0;
  bool isNextButtonEnabled = false;

  // State for the selected choice on the first page:
  String? selectedValue;

  // State for tracking if the text fields have content:
  List<bool> textFieldHasContent = [false, false, false];

  // Controllers for text fields
  final List<TextEditingController> _textControllers = [
    TextEditingController(),
    TextEditingController(),
    TextEditingController()
  ];

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Feedback Page',
      properties: {'Code': 'screen_view.feedback_page'},
    );
    _updateNextButtonState();
  }

  @override
  void dispose() {
    for (var controller in _textControllers) {
      controller.dispose();
    }
    _pageController.dispose();
    super.dispose();
  }

  void _updateNextButtonState() {
    bool enableButton = false;

    if (pageVal == 0) {
      enableButton = selectedValue != null;
    } else {
      enableButton = _textControllers[pageVal - 1].text.isNotEmpty;
    }

    setState(() {
      isNextButtonEnabled = enableButton;
    });
  }

  void onPageChange(int index) {
    setState(() {
      pageVal = index;
      _updateNextButtonState();
    });
  }

  @override
  Widget build(BuildContext context) {
    // SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    //   statusBarColor: Colors.white,
    // ));

    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    final isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom != 0;

    AddFeedbackData addFeedbackRequest = AddFeedbackData(
      feedbackData: [
        FeedbackDataItem(
          question: "How would you feel if you could no longer use this app?",
          answer: selectedValue ?? '',
          questionType: "radio",
        ),
        FeedbackDataItem(
          question:
              "How would you describe the people who would benefit most from this app?",
          answer: _textControllers[0].text,
          questionType: "text",
        ),
        FeedbackDataItem(
          question: "In your opinion, what is the biggest benefit of this app?",
          answer: _textControllers[1].text,
          questionType: "text",
        ),
        FeedbackDataItem(
          question: "How can we improve the Mental Fitness Gym for you?",
          answer: _textControllers[2].text,
          questionType: "text",
        ),
      ],
    );

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: AppColors.grey,
        ),
      ),
      body: GestureDetector(
        // Add GestureDetector here
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        behavior: HitTestBehavior.translucent,
        child: SizedBox(
          height: size.height,
          child: Padding(
            padding: EdgeInsets.fromLTRB(
              8,
              isIos ? 4 : 8,
              8,
              0,
            ),
            child: Column(
              children: [
                pageVal == 0
                    ? AppHeader(
                        currentStep: pageVal + 1,
                        totalSteps: pages.length,
                        title: "Feedback",
                        onBackTap: () => Navigator.of(context).pop(),
                      )
                    : AppHeader(
                        currentStep: pageVal + 1,
                        totalSteps: pages.length,
                        title: "Feedback",
                        // onBackTap: () => Navigator.of(context).pop(),
                      ),
                Expanded(
                  child: Container(
                    color: AppColors.navy,
                    child: Container(
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30),
                        ),
                        color: AppColors.grey,
                      ),
                      child: PageView.builder(
                        controller: _pageController,
                        onPageChanged: onPageChange,
                        physics: const NeverScrollableScrollPhysics(),
                        scrollDirection: Axis.vertical,
                        itemCount: pages.length,
                        itemBuilder: (context, index) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Padding(
                                  padding: const EdgeInsets.all(24),
                                  child: GestureDetector(
                                    onTap: () {
                                      if (pageVal > 0) {
                                        _pageController.previousPage(
                                          duration:
                                              const Duration(milliseconds: 300),
                                          curve: Curves.easeInOut,
                                        );
                                      }
                                    },
                                    child: pageVal == 0
                                        ? const SizedBox()
                                        : Image.asset(
                                            AppAssets.arrowBack,
                                            scale: 4,
                                            color: AppColors.coral,
                                          ),
                                  ),
                                ),
                              ),
                              const Spacer(flex: 2),
                              pages[index],
                              const Spacer(flex: 5),
                            ],
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: isKeyboardOpen
          ? const SizedBox()
          : Padding(
              padding: const EdgeInsets.symmetric(horizontal: 0.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: size.width,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32.0),
                      child: PrimaryButton(
                        isEnabled: isNextButtonEnabled,
                        error: !isNextButtonEnabled,
                        onErrorPressed: () {
                          SnackBarService.error(
                            context: context,
                            message: 'Please fill in all required fields',
                          );
                        },
                        text: 'Next',
                        onPressed: () {
                          if (pageVal == 3) {
                            // context.replaceRoute(FeedBackResultRoute());
                            // context.pushRoute(FeedBackResultRoute());
                            context.replaceRoute(FeedBackResultsRoute(
                              feedback: addFeedbackRequest,
                            ));
                          }
                          if (isNextButtonEnabled &&
                              pageVal < pages.length - 1) {
                            _pageController.nextPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                          // mixpanel
                          sl<MixpanelService>()
                              .trackButtonClick('Next', properties: {
                            'Page': 'Feedback Page',
                            'Code': 'click.feedback_page.next',
                            'Page Number': pageVal + 1,
                            'Total Pages': pages.length,
                          });
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  List<Widget> get pages => [
        // Page 1: Multiple Choice with Single Selection
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          // mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Gap(12),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 16,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        'How would you feel if you could no longer use this app?',
                        style: Theme.of(context)
                            .textTheme
                            .bodyEmphasis
                            .copyWith(fontSize: 18),
                      )),
                  const Gap(20),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ReflectionChoice(
                        isSelected: selectedValue == 'Very disappointed',
                        title: 'Very disappointed',
                        onTap: () {
                          setState(() {
                            selectedValue = 'Very disappointed';
                            _updateNextButtonState();
                          });
                        },
                      ),
                      // const Gap(6),
                      ReflectionChoice(
                        isSelected: selectedValue == 'Somewhat disappointed',
                        title: 'Somewhat disappointed',
                        onTap: () {
                          setState(() {
                            selectedValue = 'Somewhat disappointed';
                            _updateNextButtonState();
                          });
                        },
                      ),
                      // const Gap(6),
                      ReflectionChoice(
                        isSelected: selectedValue == 'Not disappointed',
                        title: 'Not disappointed',
                        onTap: () {
                          setState(() {
                            selectedValue = 'Not disappointed';
                            _updateNextButtonState();
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),

        // Page 2: Text Field
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                'How would you describe the people who would benefit most from this app?',
                style: Theme.of(context)
                    .textTheme
                    .bodyEmphasis
                    .copyWith(fontSize: 18),
              ),
            ),
            const Gap(24),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 0),
              child: ReflectionTextField(
                texttheme: Theme.of(context).textTheme,
                _textControllers[0],
                onChanged: (val) {
                  _updateNextButtonState();
                },
              ),
            ),
          ],
        ),

        // Page 3: Text Field
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                'In your opinion, what is the biggest benefit of this app?',
                style: Theme.of(context)
                    .textTheme
                    .bodyEmphasis
                    .copyWith(fontSize: 18),
              ),
            ),
            const Gap(24),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 0),
              child: ReflectionTextField(
                texttheme: Theme.of(context).textTheme,
                _textControllers[1],
                onChanged: (val) {
                  _updateNextButtonState();
                },
              ),
            ),
          ],
        ),

        // Page 4: Text Field
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                'How can we improve the Mental Fitness Gym for you?',
                style: Theme.of(context)
                    .textTheme
                    .bodyEmphasis
                    .copyWith(fontSize: 18),
              ),
            ),
            const Gap(24),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 0),
              child: ReflectionTextField(
                texttheme: Theme.of(context).textTheme,
                _textControllers[2],
                onChanged: (val) {
                  _updateNextButtonState();
                },
              ),
            ),
          ],
        ),
      ];
}
