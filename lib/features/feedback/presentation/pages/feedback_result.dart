import 'dart:math';
import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/core/utils/data_cache.dart';
import 'package:gotcha_mfg_app/features/exercise/presentation/blocs/reflection_data/reflection_data_cubit.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/favourite_request_model.dart';
import 'package:gotcha_mfg_app/features/feedback/data/models/add_feedback.dart';
import 'package:gotcha_mfg_app/features/feedback/presentation/blocs/feedback/feedback_cubit.dart';
import 'package:gotcha_mfg_app/features/feedback/presentation/blocs/feedback/feedback_state.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';
import 'package:video_player/video_player.dart';
import 'package:volume_controller/volume_controller.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../core/utils/snackbar_service.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../../../shared/widgets/popup.dart';

@RoutePage()
class FeedBackResultsPage extends StatefulWidget {
  final AddFeedbackData feedback;

  const FeedBackResultsPage({super.key, required this.feedback});

  @override
  State<FeedBackResultsPage> createState() => _FeedBackResultsPageState();
}

class _FeedBackResultsPageState extends State<FeedBackResultsPage> {
  VideoPlayerController? _controller; // Nullable controller
  bool _isInitialized = false;
  bool _hasError = false;
  bool _isVideoComplete = false;
  var req = FavouriteRequest(id: '', isExercise: false);

  @override
  initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Feedback Success Page',
      properties: {'Code': 'screen_view.feedback_success_page'},
    );
    context.read<FeedbackCubit>().addFeedback(widget.feedback);
    _initializeVideoController();
    _initVolume();
  }

  Future<void> _checkAndSetVolume() async {
    try {
      final volume = await VolumeController().getVolume();
      if (volume < 0.5) {
        VolumeController().setVolume(0.5);
      }
    } catch (e) {
      info('Error setting volume: $e');
    }
  }

  void _initVolume() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      unawaited(_checkAndSetVolume());
    });
  }

  Future<void> _launchStoreUrl() async {
    final Uri url = isIos
        ? Uri.parse(
            'https://apps.apple.com/us/app/the-mental-fitness-gym/id6742421861')
        : Uri.parse(
            'https://play.google.com/store/apps/details?id=com.gotcha4life.mentalfitnessgym');

    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

  String? getVideo() {
    final urls = DataCache().splashData?.data?.miscellaneous?.feedbackUrls;
    if (urls == null || urls.isEmpty) return null;
    final index = Random().nextInt(urls.length);
    return urls[index];
  }

  Future<void> _initializeVideoController() async {
    String? videoUrl = getVideo();
    if (videoUrl == null) {
      setState(() {
        _hasError = true;
      });
      return;
    }
    _controller = VideoPlayerController.networkUrl(Uri.parse(videoUrl));

    try {
      await _controller!.initialize();
      _controller!.setLooping(false);
      _controller!.addListener(_videoPositionListener);
      await _controller!.play();
      // await _controller!.setVolume(0.5);
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      info("Error initializing video: $e");
      setState(() {
        _hasError = true;
      });
    }
  }

  void _videoPositionListener() {
    if (_controller != null &&
        _controller!.value.position >= _controller!.value.duration) {
      setState(() {
        _isVideoComplete = true;
      });
    }
  }

  void _handleDonePressed() {
    final isVeryDisappointed =
        widget.feedback.feedbackData.first.answer == 'Very disappointed';

    if (isVeryDisappointed) {
      CustomCupertinoAlertDialog.yesOrNoPopup(
        context,
        title: "Rate us",
        content: "Would you like to rate our app on the store?",
        onYes: () async {
          await _launchStoreUrl();
          if (mounted) {
            context.router.replaceAll(
              [HomeRoute(index: 0)],
              updateExistingRoutes: false,
            );
          }
        },
        onNo: () {
          Navigator.pop(context);
          context.router.replaceAll(
            [HomeRoute(index: 0)],
            updateExistingRoutes: false,
          );
        },
      );
    } else {
      context.router.replaceAll(
        [HomeRoute(index: 0)],
        updateExistingRoutes: false,
      );
    }
  }

  @override
  void dispose() {
    _controller?.removeListener(_videoPositionListener);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void deactivate() {
    _controller?.pause();
    super.deactivate();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ReflectionDataCubit>();

    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    return BlocConsumer<FeedbackCubit, FeedbackState>(
        listener: (context, state) {
      if (state is FeedbackError) {
        SnackBarService.error(
          context: context,
          message: state.message ?? '',
        );
      }
      if (state is FeedbackLoaded) {
        // SnackBarService.info( // Removed SnackBar
        //   context: context,
        //   message: state.feedbackSubmitResponse.message ?? '',
        // );
      }
    }, builder: (context, state) {
      if (state is FeedbackLoading) {
        return const LoadingWidget(color: Colors.white);
      }
      return Scaffold(
        appBar: AppBar(
          toolbarHeight: 0,
          elevation: 0,
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Colors.white,
            systemNavigationBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.light,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: AppColors.grey,
          ),
        ),
        body: Padding(
          padding: EdgeInsets.only(
            top: isIos ? 4 : 8,
            left: 8,
            right: 8,
          ),
          child: Column(
            children: [
              const AppHeader(title: 'Feedback'),
              Expanded(
                child: Container(
                  color: AppColors.navy,
                  child: Container(
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                      color: AppColors.grey,
                    ),
                    child: Stack(
                      children: [
                        Positioned.fill(
                          bottom: 80, // Account for bottom button space
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 32,
                                vertical: 0,
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    'Thank you!',
                                    textAlign: TextAlign.center,
                                    style: textTheme.gothamBold
                                        .copyWith(fontSize: 26),
                                  ),
                                  const Gap(32),
                                  Container(
                                    width: size.width,
                                    height: size.width > 600
                                        ? size.height / 1.5
                                        : size.height / 1.8,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(30),
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(30),
                                      child: Stack(
                                        fit: StackFit.expand,
                                        children: [
                                          // Background image to show after video ends
                                          AnimatedOpacity(
                                            opacity:
                                                _isVideoComplete ? 1.0 : 0.0,
                                            duration: const Duration(
                                                milliseconds: 800),
                                            child: Container(
                                              color: AppColors.midBlue,
                                              width: size.width,
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Image.asset(
                                                    AppAssets.fliptextlogo,
                                                    fit: BoxFit.cover,
                                                    width: size.width / 4,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          // Video player with fade out animation
                                          AnimatedOpacity(
                                            opacity:
                                                _isVideoComplete ? 0.0 : 1.0,
                                            duration: const Duration(
                                                milliseconds: 800),
                                            child: _buildVideoPlayer(),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const Gap(24),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 24,
            horizontal: 32,
          ),
          child: SizedBox(
            width: size.width,
            child: PrimaryButton(
              text: 'Done',
              isEnabled: true,
              onPressed: _handleDonePressed,
            ),
          ),
        ),
      );
    });
  }

  Widget _buildVideoPlayer() {
    if (_hasError) {
      return Container(
        color: AppColors.grey,
        child: Center(
          child: Text(
            "Error loading video.",
            style: Theme.of(context).textTheme.bodyEmphasis,
          ),
        ),
      );
    } else if (_isInitialized && _controller != null) {
      return FittedBox(
        fit: BoxFit.cover,
        clipBehavior: Clip.hardEdge,
        child: SizedBox(
          width: _controller!.value.size.width,
          height: _controller!.value.size.height,
          child: VideoPlayer(_controller!),
        ),
      );
    } else {
      return Container(
        color: AppColors.grey,
        child: const Center(
          child: Loader(),
        ),
      );
    }
  }
}
