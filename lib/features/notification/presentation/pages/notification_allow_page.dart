import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/config/theme/app_typography.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_post_request.dart';
import 'package:gotcha_mfg_app/features/notification/presentation/blocs/notification/notification_cubit.dart';
import 'package:gotcha_mfg_app/features/notification/presentation/blocs/notification/notification_state.dart';
import 'package:gotcha_mfg_app/features/notification/presentation/pages/notification_permission_page.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:wheel_picker/wheel_picker.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/primary_button.dart';

@RoutePage()
class NotificationAllowPage extends StatefulWidget {
  const NotificationAllowPage({super.key});

  @override
  State<NotificationAllowPage> createState() => _NotificationAllowPageState();
}

class _NotificationAllowPageState extends State<NotificationAllowPage> {
  String _selectedTime = '';

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Notification Allow Page',
      properties: {'Code': 'screen_view.notification_allow_page'},
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.of(context).size;
    return BlocConsumer<NotificationCubit, NotificationState>(
        listener: (context, state) {
      // Optional: You can add listeners here if needed.
      // For example, you might show a SnackBar if there's an error.
      if (state is NotificationError) {
        SnackBarService.error(
          context: context,
          message: state.message,
        );
      }
    }, builder: (context, notificationState) {
      if (notificationState is NotificationLoading) {
        return const LoadingWidget(color: Colors.white);
      }
      return Scaffold(
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          child: Row(
            children: [
              Expanded(
                flex: 5,
                child: Container(
                  margin: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 4,
                  ),
                  child: PrimaryButton(
                    text: 'Yes',
                    onPressed: () {
                      context
                          .read<NotificationCubit>()
                          .postNotificationReminderTime(
                            NotificationPostRequestParams(
                              scheduledTime: _selectedTime,
                            ),
                          );
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) =>
                              const NotificationPermissionPage(),
                        ),
                      );
                      // mixpanel
                      sl<MixpanelService>().trackButtonClick('Yes',
                          properties: {
                            'Page': 'Notification Allow Page',
                            'Code': 'click.notification_allow_page.yes'
                          });
                    },
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Container(
                  margin: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 4,
                  ),
                  child: PrimaryButton(
                    buttonColor: AppColors.disabledGrey,
                    textColor: Colors.grey,
                    text: 'No',
                    onPressed: () {
                      context.router.replaceAll(
                        [HomeRoute(index: 0)],
                        updateExistingRoutes: false,
                      );
                      // mixpanel
                      sl<MixpanelService>().trackButtonClick('No', properties: {
                        'Page': 'Notification Allow Page',
                        'Code': 'click.notification_allow_page.no'
                      });
                      return;
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
        appBar: AppBar(
          toolbarHeight: 0,
          elevation: 0,
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Colors.white,
            statusBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.light,
            systemNavigationBarColor: AppColors.grey,
            systemNavigationBarIconBrightness: Brightness.dark,
          ),
        ),
        body: SafeArea(
          top: true,
          bottom: false,
          child: Padding(
            padding: const EdgeInsets.only(
              top: 8,
              left: 8,
              right: 8,
            ),
            child: Column(
              children: [
                // const Gap(50),
                Expanded(
                  child: Container(
                    height: size.height,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                      color: AppColors.grey,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(30.0),
                      child: Column(
                        children: [
                          const Spacer(
                            flex: 2,
                          ),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 24.0),
                            child: Text(
                              'Get a reminder to continue working on your mental fitness at the same time tomorrow ?',
                              style: textTheme.bodyEmphasis,
                              textAlign: TextAlign.center,
                            ),
                          ),
                          const Gap(48),
                          WheelPickerWidget(
                            onTimeSelected: (time) {
                              // setState(() {
                              _selectedTime = time;

                              // });
                            },
                          ),
                          const Spacer(
                            flex: 4,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}

class WheelPickerWidget extends StatefulWidget {
  final Function(String) onTimeSelected;

  const WheelPickerWidget({
    super.key,
    required this.onTimeSelected,
  });

  @override
  State<WheelPickerWidget> createState() => _WheelPickerWidgetState();
}

class _WheelPickerWidgetState extends State<WheelPickerWidget> {
  final now = TimeOfDay.now();
  int _selectedHour = 0;
  int _selectedMinute = 0;
  int _selectedAmPm = 0; // 0 for AM, 1 for PM
  String _formattedTime = ''; // Added to store formatted time

  // Round down to the nearest hour
  late final _hoursWheel = WheelPickerController(
    itemCount: 12,
    initialIndex: now.hour % 12 == 0
        ? 12
        : now.hour % 12, // Convert 0 to 12 for 12-hour format
  );

  late final _minutesWheel = WheelPickerController(
    itemCount: 60,
    initialIndex: 0,
    mounts: [_hoursWheel],
  );

  String _get24HourTime() {
    int hour = _selectedHour == 0 ? 12 : _selectedHour;
    if (_selectedAmPm == 1 && hour != 12) {
      // PM
      hour += 12;
    } else if (_selectedAmPm == 0 && hour == 12) {
      // AM and 12
      hour = 0;
    }
    return '${hour.toString().padLeft(2, '0')}:${_selectedMinute.toString().padLeft(2, '0')}';
  }

  void _updateTime() {
    _formattedTime = _get24HourTime();
    widget.onTimeSelected(_formattedTime);
  }

  @override
  void initState() {
    super.initState();
    _selectedHour = _hoursWheel.initialIndex;
    _selectedAmPm = (now.period == DayPeriod.am) ? 0 : 1;
    _updateTime(); // This will set the initial formatted time
  }

  @override
  Widget build(BuildContext context) {
    TextStyle textStyle = const TextStyle(
      fontSize: 17.0,
      height: 1.5,
    ).copyWith(
      fontSize: 17,
      fontFamily: AppTypography.gothamMedium.fontFamily,
      color: AppColors.navy,
    );

    final wheelStyle = WheelPickerStyle(
      itemExtent: textStyle.fontSize! * textStyle.height!,
      squeeze: .8,
      diameterRatio: 1.2,
      surroundingOpacity: .25,
      magnification: 1.2,
    );

    Widget itemBuilder(BuildContext context, int index) {
      return Text("$index".padLeft(2, '0'), style: textStyle);
    }

    final timeWheels = <Widget>[
      const Gap(4),
      Expanded(
        child: WheelPicker(
          builder: itemBuilder,
          controller: _hoursWheel,
          looping: true,
          style: wheelStyle,
          selectedIndexColor: AppColors.navy,
          onIndexChanged: (index, type) {
            setState(() {
              _selectedHour = index;
              _updateTime();
            });
          },
        ),
      ),
      // Text(":", style: textStyle),
      Expanded(
        child: WheelPicker(
          builder: itemBuilder,
          controller: _minutesWheel,
          looping: true,
          style: wheelStyle,
          selectedIndexColor: AppColors.navy,
          onIndexChanged: (index, type) {
            setState(() {
              _selectedMinute = index;
              _updateTime();
            });
          },
        ),
      ),
    ];

    final amPmWheel = Expanded(
      child: WheelPicker(
        itemCount: 2,
        builder: (context, index) {
          return Text(["AM", "PM"][index], style: textStyle);
        },
        initialIndex: (now.period == DayPeriod.am) ? 0 : 1,
        looping: false,
        style: wheelStyle.copyWith(
          shiftAnimationStyle: const WheelShiftAnimationStyle(
            duration: Duration(seconds: 1),
            curve: Curves.bounceOut,
          ),
        ),
        onIndexChanged: (index, type) {
          setState(() {
            _selectedAmPm = index;
            _updateTime();
          });
        },
      ),
    );

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: Colors.white,
      ),
      width: 200.0,
      height: 250.0,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Center(
          child: SizedBox(
            width: 200.0,
            height: 250.0,
            child: Stack(
              fit: StackFit.expand,
              children: [
                _centerBar(context),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 0),
                  child: Row(
                    children: [
                      ...timeWheels,
                      const SizedBox(width: 6.0),
                      amPmWheel,
                      const Gap(4),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _hoursWheel.dispose();
    _minutesWheel.dispose();
    super.dispose();
  }

  Widget _centerBar(BuildContext context) {
    return Center(
      child: Container(
        height: 34.0,
        decoration: BoxDecoration(
          color: AppColors.disabledGrey,
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
    );
  }
}
