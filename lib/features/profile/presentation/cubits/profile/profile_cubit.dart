import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_response.dart';
import 'package:gotcha_mfg_app/features/notification/domain/usecases/get_notification_usecase.dart'
    as count;
import 'package:gotcha_mfg_app/features/profile/data/models/logout_req.dart';
import 'package:gotcha_mfg_app/features/profile/domain/usecases/logout_usecase.dart';

import '../../../../../core/storage/token_manager.dart';
import '../../../../../locator.dart';
import '../../../../auth/data/models/identity_group_model.dart';
import '../../../../auth/domain/usecases/get_identity_usecase.dart';
import '../../../../notification/data/models/notification_count.dart';
import '../../../../notification/domain/usecases/get_notification_count_usecase.dart'
    as fetch;
import '../../../data/models/profile_detail_reponse.dart';
import '../../../data/models/profile_edit_request.dart';
import '../../../data/models/profile_response.dart';
import '../../../data/models/profile_update_response.dart';
import '../../../domain/usecases/get_profile_details.dart';
import '../../../domain/usecases/get_profile_links_use_case.dart';
import '../../../domain/usecases/update_profile_usecase.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  ProfileCubit(
      this._getProfileLinksUseCase,
      this._getProfileDetailUseCase,
      this._getIdentityGroupsUseCase,
      this._updateProfileUseCase,
      this._logoutUseCase,
      this._getNotificationCountUseCase,
      this._getNotificationUseCase)
      : super(ProfileInitial());

  final GetProfileLinksUseCase _getProfileLinksUseCase;
  final GetProfileDetailUseCase _getProfileDetailUseCase;
  final GetIdentityGroupsUseCase _getIdentityGroupsUseCase;
  final UpdateProfileUseCase _updateProfileUseCase;
  final LogoutUseCase _logoutUseCase;

  final fetch.GetNotificationCountUseCase _getNotificationCountUseCase;
  final count.GetNotificationUseCase _getNotificationUseCase;

  Future<void> getProfileLinks() async {
    emit(ProfileLoading());
    final result = await _getProfileLinksUseCase.call(NoParams());
    final count = await _getNotificationCountUseCase.call(NoParams());
    final notification = await _getNotificationUseCase
        .call(NotificationGetRequestParams(scheduledTime: ''));
    if (result.isSuccess) {
      emit(ProfileLinksLoaded(
          profileResponse: result.data!,
          get: notification.data!,
          count: count.data!));
    } else {
      emit(ProfileError(message: result.error!));
    }
  }

  /// Get Profile Detail
  Future<void> getProfileDetail() async {
    emit(ProfileLoading());
    final result = await _getProfileDetailUseCase.call(NoParams());
    final identity = await _getIdentityGroupsUseCase.call(NoParams());

    if (result.isSuccess & identity.isSuccess) {
      emit(ProfileDetailsLoaded(
          profileResponse: result.data!, identityResponse: identity.data!));
    } else {
      final error = result.isSuccess ? identity.error! : result.error!;
      emit(ProfileError(message: error));
    }
  }

  Future<void> updateProfile(UpdateProfileParams params) async {
    emit(ProfileLoading());
    final result = await _updateProfileUseCase.call(params);
    if (result.isSuccess) {
      emit(ProfileUpdated(result.data!));
    } else {
      emit(ProfileError(message: result.error!));
    }
  }

  Future<void> logout(LogoutRequestParams request) async {
    emit(ProfileLoading());
    final result = await _logoutUseCase.call(request);
    if (result.isSuccess) {
      sl<TokenManager>().clearAll();
      emit(ProfileLogoutLoaded());
    } else {
      emit(ProfileError(message: result.data?.message ?? ''));
    }
  }
}
